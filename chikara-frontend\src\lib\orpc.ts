import { createORPCClient } from "@orpc/client";
import { RPCLink } from "@orpc/client/fetch";
import { createTanstackQueryUtils } from "@orpc/tanstack-query";
import type { AppRouterClient } from "../../../chikara-backend/src/orpcRouter";
import { SimpleCsrfProtectionLinkPlugin } from "@orpc/client/plugins";

const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";

export const link = new RPCLink({
    url: `${baseURL}/rpc`,
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
    },
    fetch(url, options) {
        return fetch(url, {
            ...options,
            credentials: "include",
        });
    },
    plugins: [new SimpleCsrfProtectionLinkPlugin()],
});

export const client: AppRouterClient = createORPCClient(link);

export const orpc = createTanstackQueryUtils(client);

export interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
    // [key: string]: unknown;
}

export type { AppRouterClient };
